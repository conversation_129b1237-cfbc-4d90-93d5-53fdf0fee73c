"""
Character Consistency Agent
--------------------------
Analyzes stories to extract characters and generate reference images for visual consistency.
"""

import os
import json
import logging
import datetime
from typing import Optional

from crewai import Agent, Task, Crew, Process

from utils.image_generator import ImageGenerator
from utils.parsers import CharacterConsistencyDataParser
from utils.content_moderation import sanitize_character_description
from models.schema import Story, Character, CharacterReference, CharacterConsistencyData

logger = logging.getLogger(__name__)


class CharacterConsistencyAgent:
    """Agent for analyzing stories and generating character consistency data."""

    def __init__(self,
                 verbose: bool = False,
                 model: str = "gpt-4o-mini",
                 provider: str = "openai",
                 image_model: str = "black-forest-labs/FLUX-1.1-pro",
                 image_provider: str = "deepinfra"):
        """
        Initialize the Character Consistency Agent.

        Args:
            verbose (bool): Whether to enable verbose output
            model (str): LLM model to use for character analysis
            provider (str): LLM provider to use
            image_model (str): Image generation model to use for reference images
            image_provider (str): Image generation provider to use for reference images
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider
        self.image_model = image_model
        self.image_provider = image_provider
        self.llm = None  # Will be set by the main script

    def analyze_characters(self, story: Story) -> CharacterConsistencyData:
        """
        Analyze the story to extract characters and their descriptions.

        Args:
            story (Story): The story to analyze

        Returns:
            CharacterConsistencyData: Character analysis results
        """
        logger.info("Starting character analysis for story consistency")

        # Create a parser for the CharacterConsistencyData model
        parser = CharacterConsistencyDataParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the character analysis agent
        character_analyzer = Agent(
            role="Character Consistency Analyst",
            goal="Analyze the story to identify all characters and create detailed visual profiles for consistency",
            backstory="""You are an expert in character design and visual storytelling. Your task is to carefully
            analyze the story and identify all characters that appear, creating detailed visual profiles that will
            ensure consistent representation across all generated images. You focus on physical appearance,
            clothing, and distinctive features that make each character recognizable.""",
            verbose=self.verbose,
            allow_delegation=False,
            llm=self.llm
        )

        # Prepare the story text for analysis
        story_text = f"Title: {story.title}\n\n"

        for scene in story.scenes:
            story_text += f"Scene {scene.scene_number}:\n"
            story_text += f"Narration: {scene.narration}\n"
            story_text += f"Visual Description: {scene.visual_description}\n\n"

        # Create the analysis task
        analysis_task = Task(
            description=f"""Analyze the following Hindi story and identify all characters that appear in it.
            For each character, create a detailed profile including:

            1. Character name
            2. Role in the story (protagonist, antagonist, supporting character, etc.)
            3. Detailed physical description (age, height, build, hair color/style, eye color, skin tone, etc.)
            4. Clothing style and typical attire
            5. Distinctive features that make them recognizable
            6. Key personality traits that might affect visual representation
            7. Brief background relevant to visual representation
            8. List of scene numbers where the character appears

            Story to analyze:
            {story_text}

            Focus on characters that are actually described or mentioned in the story.
            Be very detailed in physical descriptions to ensure visual consistency.""",
            agent=character_analyzer,
            expected_output=format_instructions
        )

        # Execute the analysis
        crew = Crew(
            agents=[character_analyzer],
            tasks=[analysis_task],
            process=Process.sequential,
            verbose=self.verbose
        )

        result = crew.kickoff()

        # Parse the result using the Pydantic parser
        character_data = CharacterConsistencyDataParser.parse_output(parser, str(result))

        # If parsing fails, return empty data
        if character_data is None:
            logger.warning("Could not parse character analysis result, returning empty data")
            character_data = CharacterConsistencyData(
                characters=[],
                character_references=[],
                character_appearance_notes={}
            )

        logger.info(f"Character analysis complete. Found {len(character_data.characters)} characters")
        return character_data

    def generate_character_references(self,
                                      character_data: CharacterConsistencyData,
                                      output_dir: str,
                                      image_style: str = 'realistic') -> CharacterConsistencyData:
        """
        Generate reference images for all characters.

        Args:
            character_data (CharacterConsistencyData): Character data from analysis
            output_dir (str): Directory to save reference images
            image_style (str): Style for reference images

        Returns:
            CharacterConsistencyData: Updated data with reference image paths
        """
        logger.info(f"Generating reference images for {len(character_data.characters)} characters")

        # Create characters subdirectory
        characters_dir = os.path.join(output_dir, 'characters')
        os.makedirs(characters_dir, exist_ok=True)

        # Initialize image generator
        image_generator = ImageGenerator(
            model=self.image_model,
            provider=self.image_provider,
            max_concurrent_requests=5,  # Lower concurrency for reference images
            use_parallel=False  # Sequential for reference images to ensure quality
        )

        updated_references = []

        for character in character_data.characters:
            logger.info(f"Generating reference image for character: {character.name}")

            # Create detailed prompt for character reference
            reference_prompt = self._create_character_reference_prompt(character, image_style)

            # Generate filename
            safe_name = "".join(c for c in character.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_name = safe_name.replace(' ', '_')
            filename = os.path.join(characters_dir, f"character_{safe_name}_reference.png")

            try:
                # Generate the reference image
                image_data = image_generator._generate_image_from_prompt(
                    reference_prompt,
                    width=1024,
                    height=1024,
                    image_style=image_style
                )

                if image_data:
                    # Save the image
                    with open(filename, "wb") as f:
                        f.write(image_data)

                    # Create reference entry
                    reference = CharacterReference(
                        character_name=character.name,
                        reference_image_path=filename,
                        image_prompt_used=reference_prompt,
                        generation_timestamp=datetime.datetime.now().isoformat()
                    )
                    updated_references.append(reference)

                    logger.info(f"Reference image saved for {character.name}: {filename}")

                else:
                    logger.error(f"Failed to generate reference image for {character.name}")

            except Exception as e:
                logger.error(f"Error generating reference image for {character.name}: {str(e)}")

        # Update character data with references
        character_data.character_references = updated_references

        logger.info(f"Generated {len(updated_references)} character reference images")
        return character_data

    def generate_group_image(self,
                             character_data: CharacterConsistencyData,
                             output_dir: str,
                             image_style: str = 'realistic') -> Optional[str]:
        """
        Generates a single group image containing all characters from the story.

        Args:
            character_data (CharacterConsistencyData): Character data from analysis.
            output_dir (str): Directory to save the group image.
            image_style (str): Style for the group image.

        Returns:
            Optional[str]: Path to the generated group image, or None if generation fails.
        """
        if not character_data.characters:
            logger.warning("No characters found, skipping group image generation.")
            return None

        logger.info("Generating group image for all characters.")

        # Create characters subdirectory if it doesn't exist
        characters_dir = os.path.join(output_dir, 'characters')
        os.makedirs(characters_dir, exist_ok=True)

        # Initialize image generator
        image_generator = ImageGenerator(
            model=self.image_model,
            provider=self.image_provider,
            max_concurrent_requests=1, # Single request for group image
            use_parallel=False
        )

        # Create a prompt for the group image
        group_prompt_parts = [
            f"A group portrait of all main characters from the story, {image_style} style, high quality, professional character design."
        ]
        for character in character_data.characters:
            # Apply content moderation to character descriptions
            sanitized_physical = sanitize_character_description(character.physical_description)
            sanitized_clothing = sanitize_character_description(character.clothing_style)
            sanitized_features = sanitize_character_description(character.distinctive_features)
            group_prompt_parts.append(f"{character.name}: {sanitized_physical}, {sanitized_clothing}, {sanitized_features}.")

        group_prompt = " ".join(group_prompt_parts)
        group_prompt += " Each character is clearly visible and labeled with their name." # Add labeling instruction

        # Generate filename
        filename = os.path.join(characters_dir, "all_characters_group_reference.png")

        try:
            image_data = image_generator._generate_image_from_prompt(
                group_prompt,
                width=1920, # Larger size for group image
                height=1080,
                image_style=image_style
            )

            if image_data:

                with open(filename, "wb") as f:
                    f.write(image_data)

                logger.info(f"Group image saved to {filename}")
                return filename

            else:
                logger.error("Failed to generate group image.")
                return None

        except Exception as e:
            logger.error(f"Error generating group image: {str(e)}")
            return None

    def _create_character_reference_prompt(self, character: Character, image_style: str) -> str:
        """
        Create a detailed prompt for generating a character reference image.

        Args:
            character (Character): Character to create prompt for
            image_style (str): Image style to use

        Returns:
            str: Detailed image prompt
        """
        # Apply content moderation to character descriptions
        sanitized_physical = sanitize_character_description(character.physical_description)
        sanitized_clothing = sanitize_character_description(character.clothing_style)
        sanitized_features = sanitize_character_description(character.distinctive_features)

        prompt = f"""Character reference sheet for {character.name}, {character.role} in the story.

        Physical Description: {sanitized_physical}

        Clothing: {sanitized_clothing}

        Distinctive Features: {sanitized_features}

        Character portrait, front-facing view, clear and detailed, {image_style} style,
        high quality, professional character design, consistent lighting, neutral background,
        full body or upper body shot suitable for reference purposes"""

        return prompt

    def process_story_for_consistency(self,
                                      story: Story,
                                      output_dir: str,
                                      image_style: str = 'realistic') -> CharacterConsistencyData:
        """
        Complete character consistency processing pipeline.

        Args:
            story (Story): Story to process
            output_dir (str): Output directory for character data and images
            image_style (str): Style for reference images

        Returns:
            CharacterConsistencyData: Complete character consistency data
        """
        logger.info("Starting complete character consistency processing")

        # Step 1: Analyze characters
        character_data = self.analyze_characters(story)

        # Step 2: Generate reference images
        if character_data.characters:
            character_data = self.generate_character_references(character_data, output_dir, image_style)

            # Step 2.5: Generate group image
            group_image_path = self.generate_group_image(character_data, output_dir, image_style)

            if group_image_path:
                character_data.characters_group_image_path = group_image_path

        else:
            logger.warning("No characters found in story, skipping reference and group image generation")

        # Step 3: Save character data
        character_data_path = os.path.join(output_dir, 'character_consistency.json')

        with open(character_data_path, 'w', encoding='utf-8') as f:
            json.dump(character_data.model_dump(), f, ensure_ascii=False, indent=2)

        logger.info(f"Character consistency data saved to {character_data_path}")
        logger.info("Character consistency processing complete")

        return character_data
