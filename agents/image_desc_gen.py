"""
Image Description Generator
-------------------------
Converts each dialogue/cue into a detailed image prompt.
"""
import os
import logging
from typing import List

from langchain_openai import ChatOpenAI
from crewai import Agent, Task, Crew, Process

from models.schema import SceneSegmentList, ImagePrompt, EnhancedImagePrompt, CharacterConsistencyData
from utils.content_moderation import sanitize_prompt

logger = logging.getLogger(__name__)


class ImageDescriptionGenerator:
    def __init__(self,
                 image_generation_model_name: str = "black-forest-labs/FLUX-1.1-pro",
                 verbose: bool = False,
                 model: str = "gpt-4o-mini",
                 provider: str = "openai"):
        """
        Initialize the image description generator with necessary API keys.

        Args:
            image_generation_model_name (str): The name of the image generation model to use.
                Defaults to "black-forest-labs/FLUX-1.1-pro".
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.verbose = verbose
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.llm_model = model
        self.provider = provider

        # Use provided image_generation_model_name
        self.image_generation_model_name = image_generation_model_name

        # Extract the short model name for prompts (remove organization prefix if present)
        if "/" in self.image_generation_model_name:
            self.image_generation_short_model_name = self.image_generation_model_name.split("/")[-1]
        else:
            self.image_generation_short_model_name = self.image_generation_model_name

        if not self.openai_api_key:
            raise ValueError("Missing required API key for ImageDescriptionGenerator")

        # Initialize the LLM
        self.llm = ChatOpenAI(
            model=self.llm_model,
            temperature=0.5,
            api_key=self.openai_api_key
        )

    def generate(self, scene_segments: SceneSegmentList, image_style: str = 'realistic') -> List[ImagePrompt]:
        """
        Generate detailed image descriptions for each scene segment.

        Args:
            scene_segments (SceneSegmentList): List of scene segments with narration and visual cues
            image_style (str): Visual aesthetic style for generated images - realistic, anime, cartoon, etc.

        Returns:
            List[ImagePrompt]: List of scene segments with added image prompts optimized for the specified model
        """
        logger.info(f"Starting image description generation with style: {image_style}")

        # Create the image description generator agent
        desc_generator = Agent(
            role=f"{image_style.capitalize()} Visual Prompt Engineer",
            goal=f"Create detailed {image_style} style image prompts for a Hindi story",
            backstory=f"""You are an expert in visual storytelling and prompt engineering, specializing in {image_style} style imagery.
            Your task is to create detailed image prompts in {image_style} style that can be used with an image
            generation model to create compelling visuals for a Hindi story.""",
            verbose=self.verbose,
            allow_delegation=False,
            llm=self.llm
        )

        enhanced_segments = []

        # Process each segment individually
        for i, segment in enumerate(scene_segments):
            logger.info(f"Generating image description for segment {i+1}/{len(scene_segments)}")

            # Create the image description task
            desc_task = Task(
                description=f"""
                Create a detailed image prompt in {image_style} style for the following scene segment:

                Scene Number: {segment.scene_number}
                Segment Number: {segment.segment_number}
                Narration: {segment.narration}
                Visual Cue: {segment.visual_cue}
                Style: {image_style}

                Your task is to:

                1. Create a detailed image prompt that captures the essence of this scene segment in {image_style} style
                2. Include specific details about:
                   - Setting and environment
                   - Characters and their expressions
                   - Lighting and atmosphere
                   - Any important objects or elements
                   - Specific visual characteristics of the {image_style} style
                3. Format the prompt to work well with the {self.image_generation_short_model_name} image generation model
                4. Ensure the prompt will create an image appropriate for the story
                5. Maintain consistency with previous scenes if this is not the first scene
                6. Include specific style keywords that will help generate the {image_style} aesthetic

                Your output should be a detailed image prompt of 100-150 words that clearly conveys the {image_style} style.
                """,
                agent=desc_generator,
                expected_output=f"""
                A detailed image prompt in {image_style} style that can be used with the {self.image_generation_short_model_name} model.
                """,
                llm=self.llm
            )

            # Create and run the crew for this segment
            crew = Crew(
                process=Process.sequential,
                tasks=[desc_task],
                agents=[desc_generator],
                manager_llm=self.llm,
                verbose=self.verbose,
            )

            crew_output = crew.kickoff()
            result = crew_output.raw

            # Apply content moderation to the generated image prompt
            sanitized_prompt = sanitize_prompt(result.strip())

            # Create an ImagePrompt object
            image_prompt = ImagePrompt(
                scene_number=segment.scene_number,
                segment_number=segment.segment_number,
                narration=segment.narration,
                visual_cue=segment.visual_cue,
                image_prompt=sanitized_prompt,
                estimated_duration_seconds=segment.estimated_duration_seconds
            )
            enhanced_segments.append(image_prompt)

        # Log detailed information about the enhanced segments
        logger.info("Image description generation completed successfully")
        logger.info(f"Generated {len(enhanced_segments)} image descriptions")

        for i, segment in enumerate(enhanced_segments):
            logger.info(f"Image description {i+1}: Scene {segment.scene_number}, Segment {segment.segment_number}")
            # Log a preview of the image prompt (first 50 characters)
            prompt_preview = segment.image_prompt[:50] + '...' if segment.image_prompt else 'No prompt'
            logger.info(f"Image prompt preview: {prompt_preview}")

        return enhanced_segments

    def generate_with_character_consistency(self,
                                          scene_segments: SceneSegmentList,
                                          character_data: CharacterConsistencyData,
                                          image_style: str = 'realistic') -> List[EnhancedImagePrompt]:
        """
        Generate detailed image descriptions with character consistency.

        Args:
            scene_segments (SceneSegmentList): List of scene segments with narration and visual cues
            character_data (CharacterConsistencyData): Character consistency data with references
            image_style (str): Visual aesthetic style for generated images

        Returns:
            List[EnhancedImagePrompt]: List of enhanced image prompts with character consistency
        """
        logger.info(f"Starting character-consistent image description generation with style: {image_style}")

        # Create character reference mapping
        character_refs = {ref.character_name: ref for ref in character_data.character_references}
        character_profiles = {char.name: char for char in character_data.characters}

        # Create the enhanced image description generator agent
        desc_generator = Agent(
            role=f"Character-Consistent {image_style.capitalize()} Visual Prompt Engineer",
            goal=f"Create detailed {image_style} style image prompts with character consistency for a Hindi story",
            backstory=f"""You are an expert in visual storytelling and prompt engineering, specializing in {image_style} style imagery
            with character consistency. Your task is to create detailed image prompts that maintain visual consistency for characters
            across different scenes while creating compelling {image_style} style visuals for a Hindi story.""",
            verbose=self.verbose,
            allow_delegation=False,
            llm=self.llm
        )

        enhanced_segments = []

        # Process each segment individually
        for i, segment in enumerate(scene_segments):
            logger.info(f"Generating character-consistent image description for segment {i+1}/{len(scene_segments)}")

            # Identify characters in this scene
            characters_in_scene = self._identify_characters_in_scene(segment, character_profiles)

            # Get character descriptions and reference info
            character_descriptions = {}
            character_reference_paths = {}

            for char_name in characters_in_scene:
                if char_name in character_profiles:
                    char = character_profiles[char_name]
                    character_descriptions[char_name] = f"{char.physical_description}. {char.clothing_style}. {char.distinctive_features}"

                    if char_name in character_refs:
                        character_reference_paths[char_name] = character_refs[char_name].reference_image_path

            # Create character consistency context
            character_context = ""
            if characters_in_scene:
                character_context = "\n\nCHARACTER CONSISTENCY REQUIREMENTS:\n"
                for char_name in characters_in_scene:
                    if char_name in character_descriptions:
                        character_context += f"- {char_name}: {character_descriptions[char_name]}\n"
                character_context += "\nEnsure these characters appear exactly as described above for visual consistency.\n"

            # Create the enhanced image description task
            desc_task = Task(
                description=f"""
                Create a detailed image prompt in {image_style} style for the following scene segment with character consistency:

                Scene Number: {segment.scene_number}
                Segment Number: {segment.segment_number}
                Narration: {segment.narration}
                Visual Cue: {segment.visual_cue}
                Style: {image_style}
                Characters in Scene: {', '.join(characters_in_scene) if characters_in_scene else 'None identified'}
                {character_context}

                Your task is to:

                1. Create a detailed image prompt that captures the essence of this scene segment in {image_style} style
                2. Include specific details about:
                   - Setting and environment
                   - Characters and their expressions (following the exact descriptions provided above)
                   - Lighting and atmosphere
                   - Any important objects or elements
                   - Specific visual characteristics of the {image_style} style
                3. MAINTAIN CHARACTER CONSISTENCY: If characters are present, describe them exactly as specified above
                4. Format the prompt to work well with the {self.image_generation_short_model_name} image generation model
                5. Ensure the prompt will create an image appropriate for the story
                6. Include specific style keywords that will help generate the {image_style} aesthetic

                Your output should be a detailed image prompt of 150-200 words that clearly conveys the {image_style} style
                and maintains character consistency.
                """,
                agent=desc_generator,
                expected_output=f"""
                A detailed character-consistent image prompt in {image_style} style that can be used with the {self.image_generation_short_model_name} model.
                """,
                llm=self.llm
            )

            # Create and run the crew for this segment
            crew = Crew(
                process=Process.sequential,
                tasks=[desc_task],
                agents=[desc_generator],
                manager_llm=self.llm,
                verbose=self.verbose,
            )

            crew_output = crew.kickoff()
            result = crew_output.raw

            # Apply content moderation to the generated image prompt
            sanitized_prompt = sanitize_prompt(result.strip())

            # Create an EnhancedImagePrompt object
            enhanced_prompt = EnhancedImagePrompt(
                scene_number=segment.scene_number,
                segment_number=segment.segment_number,
                narration=segment.narration,
                visual_cue=segment.visual_cue,
                image_prompt=sanitized_prompt,
                estimated_duration_seconds=segment.estimated_duration_seconds,
                characters_in_scene=characters_in_scene,
                character_descriptions=character_descriptions,
                character_reference_paths=character_reference_paths
            )
            enhanced_segments.append(enhanced_prompt)

        # Log detailed information about the enhanced segments
        logger.info("Character-consistent image description generation completed successfully")
        logger.info(f"Generated {len(enhanced_segments)} enhanced image descriptions")

        return enhanced_segments

    def _identify_characters_in_scene(self, segment, character_profiles) -> List[str]:
        """
        Identify which characters appear in a given scene segment.

        Args:
            segment: Scene segment to analyze
            character_profiles: Dictionary of character profiles

        Returns:
            List[str]: Names of characters identified in the scene
        """
        characters_found = []

        # Combine narration and visual cue for analysis
        scene_text = f"{segment.narration} {segment.visual_cue}".lower()

        # Check for each character by name
        for char_name, char_profile in character_profiles.items():
            # Check if character name appears in the scene
            if char_name.lower() in scene_text:
                characters_found.append(char_name)
                continue

            # Check if character appears in the scenes_appeared list
            if segment.scene_number in char_profile.scenes_appeared:
                characters_found.append(char_name)

        return characters_found
